// Import necessary modules
import axios from "axios"
import { store } from "../store"

// Define interfaces for ProblemInfo and related structures

interface DebugSolutionResponse {
  thoughts: string[]
  old_code: string
  new_code: string
  time_complexity: string
  space_complexity: string
}

interface ProblemInfo {
  problem_type: "MCQ" | "CODING" | "SQL" | "APPLICATION_DEVELOPMENT"
  problem_statement?: string
  sql_dialect?: "T-SQL" | "PostgreSQL" | "MySQL" | "SQLite" | "Oracle" | "General"
  application_type?: "React" | "JavaScript" | "Node.js" | "Python" | "Java" | "C#" | "General"
  multiple_files?: boolean
  database_schema?: {
    tables: Array<{
      table_name: string
      columns: Array<{
        column_name: string
        data_type: string
        constraints?: string
      }>
      sample_data?: string
    }>
  }
  input_format?: {
    description?: string
    parameters?: Array<{
      name: string
      type: string
      subtype?: string
    }>
  }
  output_format?: {
    description?: string
    type?: string
    subtype?: string
  }
  constraints?: Array<{
    description: string
    parameter?: string
    range?: {
      min?: number
      max?: number
    }
  }>
  test_cases?: any // Adjust the type as needed
  choices?: string[]
  is_multi_select?: boolean
}

export async function getApiKey(): Promise<string | null> {
  return store.get("openaiApiKey")
}

// Define the extractProblemInfo function
export async function extractProblemInfo(
  imageDataList: string[]
): Promise<any> {
  const storedApiKey = store.get("openaiApiKey")
  if (!storedApiKey) {
    throw new Error("OpenAI API key not set")
  }

  // Smart image optimization for multiple screenshots
  const maxImages = 4 // Increased to 4 for better problem context
  let optimizedImageList = imageDataList

  // If too many images, prioritize the most recent ones
  if (imageDataList.length > maxImages) {
    optimizedImageList = imageDataList.slice(-maxImages)
    console.log(`Image optimization: Using ${maxImages} most recent screenshots out of ${imageDataList.length} total`)
  }

  // Prepare the image contents with optimized settings
  const imageContents = optimizedImageList.map((imageData) => ({
    type: "image_url",
    image_url: {
      url: `data:image/jpeg;base64,${imageData}`,
      detail: optimizedImageList.length > 2 ? "low" : "high" // Use low detail for 3+ images to reduce payload
    }
  }))

  // Construct the messages to send to the model
  const messages = [
    {
      role: "user",
      content: [
        {
          type: "text",
          text:
            "First determine the problem type from these categories:\n" +
            "1. MCQ (Multiple Choice Question) - Look for lettered/numbered options (A,B,C,D or 1,2,3,4), 'true/false' choices, 'select all that apply' instructions\n" +
            "2. SQL - Database queries, table operations, data manipulation. Look for keywords like SELECT, INSERT, UPDATE, DELETE, CREATE TABLE, JOIN, etc. Also identify SQL dialect (T-SQL, PostgreSQL, MySQL, etc.)\n" +
            "3. APPLICATION_DEVELOPMENT - Building applications, web development, mobile apps, software projects. Look for frameworks like React, Angular, Vue, Node.js, Django, etc.\n" +
            "4. CODING - General programming problems, algorithms, data structures\n" +
            "\n" +
            "Then extract the following information based on the type:\n" +
            "For MCQ:\n" +
            "1. Complete problem statement, including any background information and question text. If a figure (eg table, combined chart, line graph, bar graph, piechart etc) is present, extract all data relevant to answer the question text.\n" +
            "2. Available choices\n" +
            "3. Any relevant context\n" +
            "For SQL:\n" +
            "1. Problem statement\n" +
            "2. SQL dialect (T-SQL, PostgreSQL, MySQL, SQLite, Oracle, or General if unclear)\n" +
            "3. EXACT database schema/table structure - be extremely precise about:\n" +
            "   - Table names (exact spelling, case-sensitive)\n" +
            "   - Column names (exact spelling, case-sensitive)\n" +
            "   - Data types for each column\n" +
            "   - Primary keys, foreign keys, constraints\n" +
            "   - Sample data if shown in tables\n" +
            "4. Expected output format and column names\n" +
            "5. Whether multiple SQL files/queries are needed\n" +
            "6. Any specific requirements (joins, aggregations, subqueries, etc.)\n" +
            "For APPLICATION_DEVELOPMENT:\n" +
            "1. Problem statement\n" +
            "2. Application type/framework (React, JavaScript, Node.js, Python, Java, C#, or General if unclear)\n" +
            "3. Requirements and features\n" +
            "4. Whether multiple files are needed\n" +
            "5. Technology stack requirements\n" +
            "For CODING:\n" +
            "1. Problem statement\n" +
            "2. Input/Output format\n" +
            "3. Constraints\n" +
            "4. Example test cases\n"
        },
        ...imageContents
      ]
    }
  ]

  // Define the function schema
  const functions = [
    {
      name: "extract_problem_details",
      description:
        "Extract and structure the key components of a coding problem",
      parameters: {
        type: "object",
        properties: {
          problem_type: {
            type: "string",
            enum: ["MCQ", "CODING", "SQL", "APPLICATION_DEVELOPMENT"],
            description: "Type of the problem"
          },
          problem_statement: {
            type: "string",
            description:
              "The ENTIRE main problem statement describing what needs to be solved"
          },
          sql_dialect: {
            type: "string",
            enum: ["T-SQL", "PostgreSQL", "MySQL", "SQLite", "Oracle", "General"],
            description: "SQL dialect for SQL problems (only required if problem_type is SQL)"
          },
          application_type: {
            type: "string",
            enum: ["React", "JavaScript", "Node.js", "Python", "Java", "C#", "General"],
            description: "Application framework/language for APPLICATION_DEVELOPMENT problems"
          },
          multiple_files: {
            type: "boolean",
            description: "Whether the solution requires multiple files"
          },
          database_schema: {
            type: "object",
            description: "Detailed database schema for SQL problems",
            properties: {
              tables: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    table_name: {
                      type: "string",
                      description: "Exact table name (case-sensitive)"
                    },
                    columns: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          column_name: {
                            type: "string",
                            description: "Exact column name (case-sensitive)"
                          },
                          data_type: {
                            type: "string",
                            description: "Column data type (INT, VARCHAR, DATE, etc.)"
                          },
                          constraints: {
                            type: "string",
                            description: "Constraints like PRIMARY KEY, FOREIGN KEY, NOT NULL, etc."
                          }
                        },
                        required: ["column_name", "data_type"]
                      }
                    },
                    sample_data: {
                      type: "string",
                      description: "Sample data shown in the table (if any)"
                    }
                  },
                  required: ["table_name", "columns"]
                }
              }
            }
          },
          input_format: {
            type: "object",
            properties: {
              description: {
                type: "string",
                description: "Description of the input format"
              },
              parameters: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    name: {
                      type: "string",
                      description: "Name of the parameter"
                    },
                    type: {
                      type: "string",
                      enum: [
                        "number",
                        "string",
                        "array",
                        "array2d",
                        "array3d",
                        "matrix",
                        "tree",
                        "graph"
                      ],
                      description: "Type of the parameter"
                    },
                    subtype: {
                      type: "string",
                      enum: ["integer", "float", "string", "char", "boolean"],
                      description: "For arrays, specifies the type of elements"
                    }
                  },
                  required: ["name", "type"]
                }
              }
            },
            required: ["description", "parameters"]
          },
          output_format: {
            type: "object",
            properties: {
              description: {
                type: "string",
                description: "Description of the expected output format"
              },
              type: {
                type: "string",
                enum: [
                  "number",
                  "string",
                  "array",
                  "array2d",
                  "array3d",
                  "matrix",
                  "boolean"
                ],
                description: "Type of the output"
              },
              subtype: {
                type: "string",
                enum: ["integer", "float", "string", "char", "boolean"],
                description: "For arrays, specifies the type of elements"
              }
            },
            required: ["description", "type"]
          },
          constraints: {
            type: "array",
            items: {
              type: "object",
              properties: {
                description: {
                  type: "string",
                  description: "Description of the constraint"
                },
                parameter: {
                  type: "string",
                  description: "The parameter this constraint applies to"
                },
                range: {
                  type: "object",
                  properties: {
                    min: { type: "number" },
                    max: { type: "number" }
                  }
                }
              },
              required: ["description"]
            }
          },
          test_cases: {
            type: "array",
            items: {
              type: "object",
              properties: {
                input: {
                  type: "object",
                  properties: {
                    args: {
                      type: "array",
                      items: {
                        anyOf: [
                          { type: "integer" },
                          { type: "string" },
                          {
                            type: "array",
                            items: {
                              anyOf: [
                                { type: "integer" },
                                { type: "string" },
                                { type: "boolean" },
                                { type: "null" }
                              ]
                            }
                          },
                          { type: "object" },
                          { type: "boolean" },
                          { type: "null" }
                        ]
                      }
                    }
                  },
                  required: ["args"]
                },
                output: {
                  type: "object",
                  properties: {
                    result: {
                      anyOf: [
                        { type: "integer" },
                        { type: "string" },
                        {
                          type: "array",
                          items: {
                            anyOf: [
                              { type: "integer" },
                              { type: "string" },
                              { type: "boolean" },
                              { type: "null" }
                            ]
                          }
                        },
                        { type: "object" },
                        { type: "boolean" },
                        { type: "null" }
                      ]
                    }
                  },
                  required: ["result"]
                }
              },
              required: ["input", "output"]
            },
            minItems: 1
          },
          choices: {
            type: "array",
            items: {
              type: "string"
            },
            description: "Available choices for MCQ"
          },
          is_multi_select: {
            type: "boolean",
            description: "Whether multiple answers can be selected"
          }
        },
        required: ["problem_type", "problem_statement"]
      }
    }
  ]

  // Prepare the request payload
  const payload = {
    model: "gpt-4o-mini",
    messages: messages,
    functions: functions,
    function_call: { name: "extract_problem_details" },
    max_tokens: 4096
  }

  try {
    // Send the request to the completion endpoint
    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      payload,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedApiKey}`
        },
        timeout: 90000 // 90 seconds for problem extraction with multiple images
      }
    )

    // Extract the function call arguments from the response
    const functionCallArguments =
      response.data.choices[0].message.function_call.arguments

    // Return the parsed function call arguments
    return JSON.parse(functionCallArguments)
  } catch (error) {
    if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid Open AI API key."
      )
    }
    if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your OpenAI API credits and try again."
      )
    }

    throw error
  }
}

export async function generateSolutionResponses(
  problemInfo: ProblemInfo
): Promise<any> {
  try {
    const storedApiKey = store.get("openaiApiKey") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    if (problemInfo.problem_type === "MCQ") {
      const mcqPrompt = `Given this ${problemInfo.is_multi_select ? 'multi-select' : 'single-select'} multiple choice question:

Question: ${problemInfo.problem_statement}

Choices:
${problemInfo.choices?.map((choice, idx) => `${idx + 1}. ${choice}`).join('\n')}

Please analyze the question and choices carefully, then:
1. ${problemInfo.is_multi_select ? 'Identify ALL correct answers' : 'Identify the correct answer'}
2. Explain your reasoning briefly

Response should be in this format:
{
  "thoughts": [
    "Initial analysis of the question",
    "Key consideration that points to the answer(s)",
    "Final confirmation of choice(s)"
  ],
  "code": "${problemInfo.is_multi_select ? 
    'ALL correct answers with numbers, format: "2,4. Second and fourth answers are correct because..."' : 
    'The answer number and text, format: "2. Correct answer text"'
  }",
  "time_complexity": "Not applicable for MCQ",
  "space_complexity": "Not applicable for MCQ"
}`

      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: "gpt-4o-mini",
          messages: [
            {
              role: "user",
              content: mcqPrompt
            }
          ],
          temperature: 0 // Add this to get more consistent results
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${storedApiKey}`
          }
        }
      )

      const content = response.data.choices[0].message.content
      return JSON.parse(content)
    }

    // Handle SQL problems
    if (problemInfo.problem_type === "SQL") {
      const sqlDialect = problemInfo.sql_dialect || "General"
      const multipleFiles = problemInfo.multiple_files || false
      const schema = problemInfo.database_schema

      // Format schema information
      let schemaInfo = "Database schema not provided"
      if (schema?.tables && schema.tables.length > 0) {
        schemaInfo = schema.tables.map(table => {
          const columns = table.columns.map(col =>
            `  ${col.column_name} ${col.data_type}${col.constraints ? ` ${col.constraints}` : ''}`
          ).join('\n')

          let tableInfo = `Table: ${table.table_name}\n${columns}`
          if (table.sample_data) {
            tableInfo += `\nSample Data:\n${table.sample_data}`
          }
          return tableInfo
        }).join('\n\n')
      }

      const sqlPrompt = `Given this SQL problem:

Problem Statement:
${problemInfo.problem_statement ?? "Problem statement not available"}

SQL Dialect: ${sqlDialect}
Multiple Files Required: ${multipleFiles ? "Yes" : "No"}

Database Schema:
${schemaInfo}

IMPORTANT: Use the EXACT table names and column names as provided in the schema above. Pay attention to:
- Case sensitivity of table and column names
- Exact spelling of all identifiers
- Data types and constraints
- Relationships between tables

Please provide a comprehensive SQL solution. Consider:
1. The specific SQL dialect (${sqlDialect}) syntax and features
2. Database design best practices
3. Query optimization and performance
4. Proper indexing considerations
5. Data integrity and constraints
6. Exact table and column name matching

${multipleFiles ?
  'Since multiple files are required, structure your solution with appropriate file names like: schema.sql, queries.sql, procedures.sql, etc.' :
  'Provide a single SQL solution.'
}

Response should be in this format:
{
  "thoughts": [
    "Analysis of the database requirements and table structure details",
    "SQL dialect specific considerations and syntax choices",
    "Query optimization, indexing, and performance considerations"
  ],
  "code": "${multipleFiles ?
    'Multiple SQL files with descriptive names. Format: create_tables.sql\\n-- Table creation scripts\\n\\nqueries.sql\\n-- Main query solutions\\n\\nindexes.sql\\n-- Index creation for optimization' :
    'Complete SQL solution with detailed comments explaining each part'
  }",
  "time_complexity": "Query execution complexity analysis (table scans, joins, sorting operations)",
  "space_complexity": "Memory usage for query execution and result sets"
}

Format Requirements:
1. Use exact table and column names from the schema
2. Include proper SQL comments explaining complex logic
3. Consider ${sqlDialect} specific syntax and functions
4. Optimize for performance with appropriate indexes
5. Handle edge cases and data validation
6. Response must be valid JSON`

      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: "gpt-4o-mini",
          messages: [
            {
              role: "user",
              content: sqlPrompt
            }
          ],
          temperature: 0
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${storedApiKey}`
          },
          timeout: 120000 // 2 minutes timeout for SQL solutions
        }
      )

      const content = response.data.choices[0].message.content
      return JSON.parse(content)
    }

    // Handle Application Development problems
    if (problemInfo.problem_type === "APPLICATION_DEVELOPMENT") {
      const appType = problemInfo.application_type || "General"
      const multipleFiles = problemInfo.multiple_files || false

      const appPrompt = `Given this application development problem:

Problem Statement:
${problemInfo.problem_statement ?? "Problem statement not available"}

Application Type/Framework: ${appType}
Multiple Files Required: ${multipleFiles ? "Yes" : "No"}

Please provide a comprehensive application solution. Consider:
1. Best practices for ${appType} development
2. Code organization and structure
3. Error handling and validation
4. Performance considerations
5. Security best practices
6. Modern development patterns

${multipleFiles ?
  'Since multiple files are required, structure your solution with appropriate file names like: App.js, components/Header.js, utils/helpers.js, etc.' :
  'Provide a single file solution with clear structure.'
}

Response should be in this format:
{
  "thoughts": [
    "Analysis of the application requirements and architecture",
    "Framework/technology specific considerations and approach",
    "Implementation strategy and key components needed"
  ],
  "code": "${multipleFiles ?
    'Multiple application files with clear file names and purposes. Format: filename.js/.py/.java\\n// File content\\n\\nfilename2.js/.py/.java\\n// File content' :
    'Complete application solution with comments'
  }",
  "time_complexity": "Application performance analysis",
  "space_complexity": "Memory usage and resource considerations"
}`

      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: "gpt-4o-mini",
          messages: [
            {
              role: "user",
              content: appPrompt
            }
          ],
          temperature: 0
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${storedApiKey}`
          },
          timeout: 90000 // 90 seconds for application development solutions
        }
      )

      const content = response.data.choices[0].message.content
      return JSON.parse(content)
    }

    // Handle coding problems
    const promptContent = `Given the following coding problem:

Problem Statement:
${problemInfo.problem_statement ?? "Problem statement not available"}

Input Format:
${problemInfo.input_format?.description ?? "Input format not available"}
Parameters:
${
  problemInfo.input_format?.parameters
    ?.map((p) => `- ${p.name}: ${p.type}${p.subtype ? ` of ${p.subtype}` : ""}`)
    .join("\n") ?? "No parameters available"
}

Output Format:
${problemInfo.output_format?.description ?? "Output format not available"}
Returns: ${problemInfo.output_format?.type ?? "Type not specified"}${
      problemInfo.output_format?.subtype
        ? ` of ${problemInfo.output_format.subtype}`
        : ""
    }

Constraints:
${
  problemInfo.constraints
    ?.map((c) => {
      let constraintStr = `- ${c.description}`
      if (c.range) {
        constraintStr += ` (${c.parameter}: ${c.range.min} to ${c.range.max})`
      }
      return constraintStr
    })
    .join("\n") ?? "No constraints specified"
}

Test Cases:
${JSON.stringify(problemInfo.test_cases ?? "No test cases available", null, 2)}

Generate a solution in this format:
{
  "thoughts": [
    "First thought showing recognition of the problem and core challenge",
    "Second thought naming specific algorithm/data structure being considered",
    "Third thought showing confidence in approach while acknowledging details needed"
  ],
  "code": "The Python solution with comments explaining the code",
  "time_complexity": "The time complexity in form O(_) because _",
  "space_complexity": "The space complexity in form O(_) because _"
}

Format Requirements:
1. Use actual line breaks in code field
2. Indent code properly with spaces
3. Include clear code comments
4. Response must be valid JSON
5. Return only the JSON object with no markdown or other formatting`

    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      {
        model: "gpt-4o-mini",
        messages: [
          {
            role: "user",
            content: promptContent
          }
        ]
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedApiKey}`
        },
        timeout: 90000 // 90 seconds for solution generation
      }
    )

    const content = response.data.choices[0].message.content
    return JSON.parse(content)
  } catch (error: any) {
    if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid Open AI API key."
      )
    }
    if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your OpenAI API credits and try again."
      )
    }
    console.error("Error details:", error)
    throw new Error(`Error generating solutions: ${error.message}`)
  }
}

export async function debugSolutionResponses(
  imageDataList: string[],
  problemInfo: ProblemInfo,
  previousSolution?: {
    code: string
    thoughts: string[]
    time_complexity: string
    space_complexity: string
  }
): Promise<DebugSolutionResponse> {
  // Add retry logic for debugging
  const maxRetries = 2
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await debugSolutionResponsesInternal(imageDataList, problemInfo, previousSolution)
    } catch (error: any) {
      lastError = error
      console.warn(`Debug attempt ${attempt} failed:`, error.message)

      // If it's the last attempt, throw the error
      if (attempt === maxRetries) {
        throw error
      }

      // Wait a bit before retrying
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  throw lastError || new Error("Debug failed after all retries")
}

async function debugSolutionResponsesInternal(
  imageDataList: string[],
  problemInfo: ProblemInfo,
  previousSolution?: {
    code: string
    thoughts: string[]
    time_complexity: string
    space_complexity: string
  }
): Promise<DebugSolutionResponse> {
  if (problemInfo.problem_type === "MCQ") {
    return {
      thoughts: ["MCQ solutions don't require debugging"],
      old_code: "Not applicable for MCQ",
      new_code: "Not applicable for MCQ",
      time_complexity: "Not applicable for MCQ",
      space_complexity: "Not applicable for MCQ"
    }
  }

  // SQL problems DO require debugging - remove this block to allow debugging

  if (problemInfo.problem_type === "APPLICATION_DEVELOPMENT") {
    return {
      thoughts: ["Application development solutions don't require debugging in this context"],
      old_code: "Not applicable for Application Development problems",
      new_code: "Not applicable for Application Development problems",
      time_complexity: "Not applicable for Application Development problems",
      space_complexity: "Not applicable for Application Development problems"
    }
  }

  // Optimize images for debugging - prioritize most recent screenshots
  const maxDebugImages = 2 // Limit to 2 images for debugging to reduce payload size
  const optimizedImageList = imageDataList.slice(-maxDebugImages) // Take the last 2 images

  if (imageDataList.length > maxDebugImages) {
    console.log(`Debug optimization: Using ${maxDebugImages} most recent screenshots out of ${imageDataList.length} total`)
  }

  // Process images for inclusion in prompt
  const imageContents = optimizedImageList.map((imageData) => ({
    type: "image_url",
    image_url: {
      url: `data:image/jpeg;base64,${imageData}`,
      detail: "high" // High detail for better code/error recognition
    }
  }))

  // Build the prompt with error handling
  const problemStatement =
    problemInfo.problem_statement ?? "Problem statement not available"

  const inputFormatDescription =
    problemInfo.input_format?.description ??
    "Input format description not available"

  const inputParameters = problemInfo.input_format?.parameters
    ? problemInfo.input_format.parameters
        .map(
          (p) => `- ${p.name}: ${p.type}${p.subtype ? ` of ${p.subtype}` : ""}`
        )
        .join(" ")
    : "Input parameters not available"

  const outputFormatDescription =
    problemInfo.output_format?.description ??
    "Output format description not available"

  const returns = problemInfo.output_format?.type
    ? `Returns: ${problemInfo.output_format.type}${
        problemInfo.output_format.subtype
          ? ` of ${problemInfo.output_format.subtype}`
          : ""
      }`
    : "Returns: Output type not available"

  const constraints = problemInfo.constraints
    ? problemInfo.constraints
        .map((c) => {
          let constraintStr = `- ${c.description}`
          if (c.range) {
            constraintStr += ` (${c.parameter}: ${c.range.min} to ${c.range.max})`
          }
          return constraintStr
        })
        .join(" ")
    : "Constraints not available"

  let exampleTestCases = "Test cases not available"
  if (problemInfo.test_cases) {
    try {
      exampleTestCases = JSON.stringify(problemInfo.test_cases, null, 2)
    } catch {
      exampleTestCases = "Test cases not available"
    }
  }

  // Construct the debug prompt based on problem type
  let debugPrompt = ""

  if (problemInfo.problem_type === "SQL") {
    const sqlDialect = problemInfo.sql_dialect || "General"
    const schema = problemInfo.database_schema

    // Format schema information for SQL debugging
    let schemaInfo = "Database schema not provided"
    if (schema?.tables && schema.tables.length > 0) {
      schemaInfo = schema.tables.map(table => {
        const columns = table.columns.map(col =>
          `  ${col.column_name} ${col.data_type}${col.constraints ? ` ${col.constraints}` : ''}`
        ).join('\n')

        let tableInfo = `Table: ${table.table_name}\n${columns}`
        if (table.sample_data) {
          tableInfo += `\nSample Data:\n${table.sample_data}`
        }
        return tableInfo
      }).join('\n\n')
    }

    // Format previous solution context for SQL
    let previousSolutionContext = ""
    if (previousSolution) {
      previousSolutionContext = `

PREVIOUS SOLUTION CONTEXT:
Original Code Generated:
${previousSolution.code}

Original Reasoning:
${previousSolution.thoughts.map((thought, idx) => `${idx + 1}. ${thought}`).join('\n')}

Original Complexity Analysis:
- Time: ${previousSolution.time_complexity}
- Space: ${previousSolution.space_complexity}

Your task is to debug and improve this previous solution based on any errors or issues you see in the new screenshots.`
    }

    debugPrompt = `
Given the following SQL problem and its visual representation:

Problem Statement:
${problemStatement}

SQL Dialect: ${sqlDialect}

Database Schema:
${schemaInfo}
${previousSolutionContext}

First extract and analyze the SQL code shown in the image(s). Look carefully for:
- The exact SQL code you wrote
- Any error messages displayed on screen (syntax errors, runtime errors, etc.)
- Test results or query output
- Database error messages or warnings
- Exact table names and column names (case-sensitive)
- SQL syntax specific to ${sqlDialect}
- Query logic and joins
- Performance considerations

If you see error messages or failed tests in the screenshots, use those as hints to fix the issues. Then create an improved version while maintaining the same general approach. The old_code should be ONLY the exact SQL that you see on the screen. Make all improvements in the new_code field based on any errors or issues you observe.

Focus on:
1. Correcting any syntax errors
2. Optimizing query performance
3. Ensuring exact table/column name matching
4. Adding helpful SQL comments on changed lines
5. Proper ${sqlDialect} dialect usage`
  } else {
    // Format previous solution context for CODING problems
    let previousSolutionContext = ""
    if (previousSolution) {
      previousSolutionContext = `

PREVIOUS SOLUTION CONTEXT:
Original Code Generated:
${previousSolution.code}

Original Reasoning:
${previousSolution.thoughts.map((thought, idx) => `${idx + 1}. ${thought}`).join('\n')}

Original Complexity Analysis:
- Time: ${previousSolution.time_complexity}
- Space: ${previousSolution.space_complexity}

Your task is to debug and improve this previous solution based on any errors or issues you see in the new screenshots.`
    }

    // Default debug prompt for CODING problems
    debugPrompt = `
Given the following coding problem and its visual representation:

Problem Statement:
${problemStatement}

Input Format:
${inputFormatDescription}
Parameters:
${inputParameters}

Output Format:
${outputFormatDescription}
${returns}

Constraints:
${constraints}

Example Test Cases:
${exampleTestCases}
${previousSolutionContext}

First extract and analyze the code shown in the image(s). Look carefully for:
- The exact code you wrote
- Any error messages displayed on screen (compilation errors, runtime errors, exceptions, etc.)
- Test results (passed/failed tests, expected vs actual output)
- Console output or debugging information
- IDE warnings or suggestions
- Stack traces or error details

If you see error messages, failed tests, or other issues in the screenshots, use those as hints to identify and fix the problems. Then create an improved version while maintaining the same general approach and structure. The old code you save should ONLY be the exact code that you see on the screen, regardless of any optimizations or changes you make. Make all your changes in the new_code field. You should use the image that has the most recent, longest version of the code, making sure to combine multiple images if necessary.
Focus on keeping the solution syntactically similar but with optimizations and INLINE comments ONLY ON lines of code that were changed. Make sure there are no extra line breaks and all the code that is unchanged is in the same line as it was in the original code.`
  }

  // Add formatting notes to the debug prompt
  debugPrompt += `

IMPORTANT FORMATTING NOTES:
1. Use actual line breaks (press enter for new lines) in both old_code and new_code
2. Maintain proper indentation with spaces in both code blocks
3. Add inline comments ONLY on changed lines in new_code
4. The entire response must be valid JSON that can be parsed`

  // Construct the messages array
  const messages = [
    {
      role: "user",
      content: [
        {
          type: "text",
          text: debugPrompt
        },
        ...imageContents
      ]
    }
  ]

  // Define the function schema
  const functions = [
    {
      name: "provide_solution",
      description:
        "Debug based on the problem and provide a solution to the coding problem",
      parameters: {
        type: "object",
        properties: {
          thoughts: {
            type: "array",
            items: { type: "string" },
            description:
              "Share up to 3 key thoughts as you work through solving this problem for the first time. Write in the voice of someone actively reasoning through their approach, using natural pauses, uncertainty, and casual language that shows real-time problem solving. Each thought must be max 100 characters and be full sentences that don't sound choppy when read aloud.",
            maxItems: 3,
            thoughtGuidelines: [
              "First thought should capture that initial moment of recognition - connecting it to something familiar or identifying the core challenge. Include verbal cues like 'hmm' or 'this reminds me of' that show active thinking.",
              "Second thought must explore your emerging strategy and MUST explicitly name the algorithm or data structure being considered. Show both knowledge and uncertainty - like 'I could probably use a heap here, but I'm worried about...'",
              "Third thought should show satisfaction at having a direction while acknowledging you still need to work out specifics - like 'Okay, I think I see how this could work...'"
            ]
          },
          old_code: {
            type: "string",
            description:
              "The exact code implementation found in the image. There should be no additional lines of code added, this should only contain the code that is visible from the images, regardless of correctness or any fixes you can make. Include every line of code that are visible in the image.  You should use the image that has the most recent, longest version of the code, making sure to combine multiple images if necessary."
          },
          new_code: {
            type: "string",
            description:
              "The improved code implementation with in-line comments only on lines of code that were changed"
          },
          time_complexity: {
            type: "string",
            description:
              "Time complexity with explanation, format as 'O(_) because _.' Importantly, if there were slight optimizations in the complexity that don't affect the overall complexity, MENTION THEM."
          },
          space_complexity: {
            type: "string",
            description:
              "Space complexity with explanation, format as 'O(_) because _' Importantly, if there were slight optimizations in the complexity that don't affect the overall complexity, MENTION THEM."
          }
        },
        required: [
          "thoughts",
          "old_code",
          "new_code",
          "time_complexity",
          "space_complexity"
        ]
      }
    }
  ]

  // Prepare the payload for the API call
  const payload = {
    model: "gpt-4o-mini",
    messages: messages,
    max_tokens: 4000,
    temperature: 0,
    functions: functions,
    function_call: { name: "provide_solution" }
  }

  try {
    // Send the request to the OpenAI API
    const storedApiKey = store.get("openaiApiKey") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      payload,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedApiKey}`
        },
        timeout: 120000 // 2 minutes timeout for debugging
      }
    )

    // Extract the function call arguments from the response
    const functionCallArguments =
      response.data.choices[0].message.function_call.arguments

    // Parse the response with better error handling
    let parsedResponse
    try {
      parsedResponse = JSON.parse(functionCallArguments)
    } catch (parseError) {
      console.error("Failed to parse debug function call arguments:", functionCallArguments)
      console.error("Parse error:", parseError)
      throw new Error("AI response format is invalid. Please try again with clearer screenshots.")
    }

    // Validate required fields
    if (!parsedResponse.thoughts || !parsedResponse.old_code || !parsedResponse.new_code) {
      throw new Error("AI response is missing required fields. Please try again.")
    }

    return parsedResponse as DebugSolutionResponse
  } catch (error: any) {
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      throw new Error("Request timed out. The AI is taking too long to process your screenshots. Please try again with fewer or clearer screenshots.")
    } else if (error.response?.status === 404) {
      throw new Error(
        "API endpoint not found. Please check the model name and URL."
      )
    } else if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid Open AI API key."
      )
    } else if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your OpenAI API credits and try again."
      )
    } else {
      throw new Error(
        `OpenAI API error: ${
          error.response?.data?.error?.message || error.message
        }`
      )
    }
  }
}
